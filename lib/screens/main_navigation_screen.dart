import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'chat_list_screen.dart';
import 'create_post.dart';
import 'search_screen.dart';
import 'profile_screen.dart';
import 'wallet_screen.dart';
import '../widgets/home/<USER>';
import '../services/wallet_service.dart';
import '../services/post_service.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/home/<USER>';
import '../services/category_preference_service.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  late List<Widget> _screens;

  // Double back to exit functionality
  DateTime? _lastBackPressed;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    final currentUid = FirebaseAuth.instance.currentUser?.uid ?? '';
    _screens = [
      const HomeContent(), // Home content without bottom nav
      const WalletScreen(), // Re Up tab
      const CreatePostScreen(),
      const SearchScreen(),
      ProfileScreen(userId: currentUid),
    ];
  }

  Future<void> _initializeServices() async {
    try {
      await WalletService().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  void _onTabTapped(int index) {
    if (_currentIndex != index) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentIndex = index;
      });
    }
  }

  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    const backPressDuration = Duration(seconds: 2);

    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > backPressDuration) {
      _lastBackPressed = now;

      // Show snackbar with exit message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Press back again to exit'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Don't exit
    }

    return true; // Exit the app
  }

  Widget _buildCompactDrawer() {
    final user = FirebaseAuth.instance.currentUser;

    return Container(
      color: Colors.grey.shade50,
      child: Column(
        children: [
          // Header with profile
          Container(
            padding: const EdgeInsets.fromLTRB(8, 50, 8, 20),
            child: Column(
              children: [
                // Profile avatar
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.grey.shade300,
                  child: const Icon(
                    Icons.person,
                    size: 20,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                // Username (truncated)
                Text(
                  user?.displayName?.split(' ').first ?? 'User',
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Navigation icons
          Expanded(
            child: Column(
              children: [
                _buildCompactNavItem(Icons.home, 0),
                _buildCompactNavItem(Icons.account_balance_wallet, 1),
                _buildCompactNavItem(Icons.add_box_outlined, 2),
                _buildCompactNavItem(Icons.search, 3),
                _buildCompactNavItem(Icons.person_outline, 4),
              ],
            ),
          ),

          // Logout button
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: IconButton(
              onPressed: () {
                FirebaseAuth.instance.signOut();
                Navigator.of(context).pushReplacementNamed('/login');
              },
              icon: const Icon(Icons.logout, size: 20),
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactNavItem(IconData icon, int index) {
    final isSelected = _currentIndex == index;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Material(
        color:
            isSelected
                ? const Color(0xFF4C5DFF).withValues(alpha: 0.1)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => _onTabTapped(index),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Icon(
              icon,
              size: 20,
              color: isSelected ? const Color(0xFF4C5DFF) : Colors.grey,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldExit = await _onWillPop();
        if (shouldExit && mounted) {
          // Exit the app
          SystemNavigator.pop();
        }
      },
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth < 768;

          return Scaffold(
            body: Row(
              children: [
                // Permanent Drawer - always visible
                Container(
                  width: isMobile ? 80 : (kIsWeb ? 300 : 200),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border(
                      right: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child:
                      isMobile ? _buildCompactDrawer() : const ProfileDrawer(),
                ),
                // Main Content
                Expanded(
                  child: Column(
                    children: [
                      // Main screen content
                      Expanded(
                        child: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 250),
                          transitionBuilder: (child, animation) {
                            return SlideTransition(
                              position: Tween<Offset>(
                                begin: const Offset(0.05, 0),
                                end: Offset.zero,
                              ).animate(
                                CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeInOutCubic,
                                ),
                              ),
                              child: FadeTransition(
                                opacity: animation,
                                child: child,
                              ),
                            );
                          },
                          child: IndexedStack(
                            key: ValueKey(_currentIndex),
                            index: _currentIndex,
                            children: _screens,
                          ),
                        ),
                      ),
                      // Bottom Navigation - only on mobile
                      if (isMobile)
                        Container(
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(color: Colors.grey.shade200),
                            ),
                          ),
                          child: BottomNavigationBar(
                            type: BottomNavigationBarType.fixed,
                            selectedItemColor: const Color(0xFF4C5DFF),
                            unselectedItemColor: Colors.grey,
                            currentIndex: _currentIndex,
                            onTap: _onTabTapped,
                            elevation: 0,
                            backgroundColor: Colors.white,
                            items: const [
                              BottomNavigationBarItem(
                                icon: Icon(Icons.home),
                                label: '',
                              ),
                              BottomNavigationBarItem(
                                icon: Icon(Icons.account_balance_wallet),
                                label: '',
                              ),
                              BottomNavigationBarItem(
                                icon: Icon(Icons.add_box_outlined),
                                label: '',
                              ),
                              BottomNavigationBarItem(
                                icon: Icon(Icons.search),
                                label: '',
                              ),
                              BottomNavigationBarItem(
                                icon: Icon(Icons.person_outline),
                                label: '',
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
