import 'package:flutter/material.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/widgets/top_paid_post_container.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';
import 'category_data.dart';
import 'horizontal_categories_section.dart';
import 'posts_feed.dart';

/// Explore Tab Widget
class ExploreTab extends StatelessWidget {
  final String selectedCategory;

  const ExploreTab({super.key, required this.selectedCategory});

  @override
  Widget build(BuildContext context) {
    final PostService postService = PostService();
    final topPost = postService.getTopPaidPostForCategory(selectedCategory);

    print(
      'ExploreTab: Building for category "$selectedCategory", topPost: ${topPost != null ? "found" : "null"}',
    );

    return Column(
      children: [
        // Category Header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Icon(Icons.category, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              Text(
                selectedCategory,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
        // Top Paid Post Container (24-hour system)
        if (topPost != null)
          TopPaidPostContainer(
            category: selectedCategory,
            topPost: topPost,
            onTap: () {
              // Navigate to post detail view
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PostDetailScreen(post: topPost),
                ),
              );
            },
          ),
        Expanded(child: PostsFeed(category: selectedCategory)),
      ],
    );
  }
}
