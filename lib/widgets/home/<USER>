import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:money_mouthy_two/services/wallet_service.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/category_preference_service.dart';

import 'home_app_bar.dart';
import 'home_tab_bar.dart';
import 'explore_tab.dart';
import 'following_tab.dart';
import 'category_data.dart';

/// Home Content Widget - Contains the main home screen logic without bottom navigation
class HomeContent extends StatefulWidget {
  const HomeContent({super.key});

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent>
    with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int currentCategoryIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadUserCategory();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      await WalletService().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  Future<void> _loadUserCategory() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userDoc =
            await FirebaseFirestore.instance
                .collection('users')
                .doc(user.uid)
                .get();

        if (userDoc.exists) {
          final userData = userDoc.data();
          final savedCategory = userData?['selectedCategory'];

          if (savedCategory != null && mounted) {
            // Find the index of the saved category
            final categoryIndex = Categories.all.indexWhere(
              (cat) => cat.name == savedCategory,
            );

            if (categoryIndex != -1) {
              setState(() {
                currentCategoryIndex = categoryIndex;
              });
              print(
                'HomeContent: Loaded saved category "$savedCategory" at index $categoryIndex',
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading user category: $e');
    }
  }

  void _onCategorySelected(int index) {
    setState(() {
      currentCategoryIndex = index;
    });

    // Save the selected category to Firebase and SharedPreferences
    _saveSelectedCategory(index);
  }

  Future<void> _saveSelectedCategory(int index) async {
    try {
      final selectedCategory = Categories.all[index].name;
      final user = FirebaseAuth.instance.currentUser;

      if (user != null) {
        // Save to Firebase
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .update({
              'selectedCategory': selectedCategory,
              'updatedAt': FieldValue.serverTimestamp(),
            });

        // Also save to SharedPreferences for faster access
        await CategoryPreferenceService.saveLastSelectedCategory(
          selectedCategory,
        );

        print(
          'HomeContent: Saved category "$selectedCategory" to Firebase and SharedPreferences',
        );
      }
    } catch (e) {
      debugPrint('Error saving selected category: $e');
    }
  }

  /// Reset category to Politics (used by main navigation)
  void resetToPolitics() {
    setState(() {
      final politicsIndex = Categories.all.indexWhere(
        (cat) => cat.name == 'Politics',
      );
      if (politicsIndex != -1) {
        currentCategoryIndex = politicsIndex;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
      body: _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              ExploreTab(
                selectedCategory: Categories.all[currentCategoryIndex].name,
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}
