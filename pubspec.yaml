name: money_mouthy_two
description: "A new Flutter project."
publish_to: "none"

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  firebase_core: ^2.27.0
  firebase_auth: ^4.18.0
  cloud_firestore: ^4.15.0
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  # Payment Gateway
  flutter_stripe: ^10.1.1
  http: ^1.1.0
  # Wallet & Transactions
  sqflite: ^2.3.0
  path: ^1.8.3
  intl: ^0.19.0
  # firebase_core: ^2.24.2
  # firebase_auth: ^4.15.0
  # cloud_firestore: ^4.13.6
  firebase_storage: ^11.6.0
  image_picker: ^1.1.1
  font_awesome_flutter: ^10.8.0
  share_plus: ^10.0.0
  url_launcher: ^6.1.11
  video_player: ^2.10.0
  video_thumbnail: ^0.5.3
  path_provider: ^2.1.1
  youtube_player_iframe: ^5.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/money_mouth.png"
  min_sdk_android: 21 # android min sdk min: 21

flutter:
  uses-material-design: true
  assets:
    - assets/images/
